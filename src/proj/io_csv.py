"""Generic CSV loader with auto-detection and normalization."""

import pandas as pd
import re
from typing import <PERSON><PERSON>, Dict, Any, List


def normalize_header(header: str) -> str:
    """Convert header to snake_case."""
    # Remove special characters and replace with underscores
    header = re.sub(r'[^\w\s]', '_', header)
    # Replace spaces with underscores
    header = re.sub(r'\s+', '_', header)
    # Convert to lowercase
    header = header.lower()
    # Remove multiple consecutive underscores
    header = re.sub(r'_+', '_', header)
    # Remove leading/trailing underscores
    header = header.strip('_')
    return header


def load_csv(path: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Load CSV with auto-detection and normalization.
    
    Args:
        path: Path to CSV file
        
    Returns:
        Tuple of (DataFrame, schema_dict)
        schema_dict contains 'columns' and 'dtypes'
    """
    # Try different delimiters
    delimiters = [',', ';', '\t', '|']
    df = None
    
    for delimiter in delimiters:
        try:
            temp_df = pd.read_csv(path, delimiter=delimiter, nrows=5)
            # Check if we have more than one column (good delimiter detection)
            if len(temp_df.columns) > 1:
                df = pd.read_csv(path, delimiter=delimiter)
                break
        except Exception:
            continue
    
    if df is None:
        # Fallback to default comma delimiter
        df = pd.read_csv(path)
    
    # Drop empty Unnamed columns
    unnamed_cols = [col for col in df.columns if col.startswith('Unnamed:')]
    if unnamed_cols:
        df = df.drop(columns=unnamed_cols)
    
    # Normalize headers to snake_case
    df.columns = [normalize_header(col) for col in df.columns]
    
    # Create schema dictionary
    schema = {
        'columns': list(df.columns),
        'dtypes': {col: str(dtype) for col, dtype in df.dtypes.items()},
        'shape': df.shape,
        'null_counts': df.isnull().sum().to_dict()
    }
    
    return df, schema


def load_player_props(path: str) -> Tuple[pd.DataFrame, List[str]]:
    """
    Load player props CSV with validation.

    Expected columns: [player_name, team, opponent, position, market, line,
                      over_odds, under_odds, book, timestamp_iso]

    Args:
        path: Path to player props CSV file

    Returns:
        Tuple of (DataFrame, issues_list)
    """
    df, _ = load_csv(path)
    issues = []

    # Required columns
    required_cols = ['player_name', 'team', 'opponent', 'position', 'market',
                    'line', 'over_odds', 'under_odds', 'book', 'timestamp_iso']

    # Check for required columns
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        issues.append(f"Missing required columns: {missing_cols}")

    # Validate data types and ranges
    if 'line' in df.columns:
        non_numeric_lines = df[pd.to_numeric(df['line'], errors='coerce').isna()]
        if not non_numeric_lines.empty:
            issues.append(f"Non-numeric values in 'line' column: {len(non_numeric_lines)} rows")

    if 'over_odds' in df.columns:
        invalid_odds = df[(df['over_odds'] < -1000) | (df['over_odds'] > 1000) | (df['over_odds'] == 0)]
        if not invalid_odds.empty:
            issues.append(f"Invalid over_odds values: {len(invalid_odds)} rows")

    if 'under_odds' in df.columns:
        invalid_odds = df[(df['under_odds'] < -1000) | (df['under_odds'] > 1000) | (df['under_odds'] == 0)]
        if not invalid_odds.empty:
            issues.append(f"Invalid under_odds values: {len(invalid_odds)} rows")

    # Check for missing critical data
    if 'player_name' in df.columns:
        missing_names = df[df['player_name'].isna() | (df['player_name'] == '')]
        if not missing_names.empty:
            issues.append(f"Missing player names: {len(missing_names)} rows")

    return df, issues


def load_team_props(path: str) -> Tuple[pd.DataFrame, List[str]]:
    """
    Load team props CSV with validation.

    Expected columns: [team, opponent, market, line, over_odds, under_odds,
                      book, timestamp_iso]

    Args:
        path: Path to team props CSV file

    Returns:
        Tuple of (DataFrame, issues_list)
    """
    df, _ = load_csv(path)
    issues = []

    # Required columns
    required_cols = ['team', 'opponent', 'market', 'line', 'over_odds',
                    'under_odds', 'book', 'timestamp_iso']

    # Check for required columns
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        issues.append(f"Missing required columns: {missing_cols}")

    # Validate data types and ranges
    if 'line' in df.columns:
        non_numeric_lines = df[pd.to_numeric(df['line'], errors='coerce').isna()]
        if not non_numeric_lines.empty:
            issues.append(f"Non-numeric values in 'line' column: {len(non_numeric_lines)} rows")

    # Validate team names
    if 'team' in df.columns:
        missing_teams = df[df['team'].isna() | (df['team'] == '')]
        if not missing_teams.empty:
            issues.append(f"Missing team names: {len(missing_teams)} rows")

    # Check for reasonable market types
    if 'market' in df.columns:
        valid_markets = ['team_total', 'pass_yds', 'rush_yds', 'team_sacks',
                        'turnovers', 'first_downs', 'penalties']
        invalid_markets = df[~df['market'].isin(valid_markets)]
        if not invalid_markets.empty:
            unique_invalid = invalid_markets['market'].unique()
            issues.append(f"Unknown market types: {list(unique_invalid)}")

    return df, issues


def load_roles_notes(path: str) -> Tuple[pd.DataFrame, List[str]]:
    """
    Load player roles and notes CSV.

    Expected columns: [player_name, team, position, expected_snap_pct,
                      role_notes, slot_outside_flag, target_share_prior]

    Args:
        path: Path to roles CSV file

    Returns:
        Tuple of (DataFrame, issues_list)
    """
    df, _ = load_csv(path)
    issues = []

    # Required columns
    required_cols = ['player_name', 'team', 'position']
    optional_cols = ['expected_snap_pct', 'role_notes', 'slot_outside_flag', 'target_share_prior']

    # Check for required columns
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        issues.append(f"Missing required columns: {missing_cols}")

    # Validate snap percentage
    if 'expected_snap_pct' in df.columns:
        invalid_snaps = df[(df['expected_snap_pct'] < 0) | (df['expected_snap_pct'] > 1)]
        if not invalid_snaps.empty:
            issues.append(f"Invalid snap percentages (should be 0-1): {len(invalid_snaps)} rows")

    # Validate target share prior
    if 'target_share_prior' in df.columns:
        invalid_shares = df[(df['target_share_prior'] < 0) | (df['target_share_prior'] > 1)]
        if not invalid_shares.empty:
            issues.append(f"Invalid target share priors (should be 0-1): {len(invalid_shares)} rows")

    return df, issues
