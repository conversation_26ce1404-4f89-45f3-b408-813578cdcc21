"""Command line interface for NFL projections."""

import argparse
import json
import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
from dotenv import load_dotenv

from .io_csv import load_csv, load_player_props, load_team_props, load_roles_notes
from .fetch_odds import get_totals_spreads, implied_team_totals
from .needs import NeedsDetector
from .joiners import map_team_props_to_games, reconcile_player_team_totals
from .model import ContextAwareModel
from .priors import HierarchicalPriors
from .sleeper import get_players, get_state, get_trending, depth_charts_for_teams
from .backtest import (
    load_fc_week1_2024, compute_residuals, summarize_residuals,
    build_residual_adjusters, write_backtest_report
)


def cmd_load(args):
    """Load and analyze CSV file."""
    try:
        df, schema = load_csv(args.csv)

        print(f"Successfully loaded CSV: {args.csv}")
        print(f"Shape: {schema['shape']}")
        print("\nSchema:")
        print(f"Columns: {schema['columns']}")
        print("\nData types:")
        for col, dtype in schema['dtypes'].items():
            print(f"  {col}: {dtype}")

        print("\nNull counts:")
        for col, null_count in schema['null_counts'].items():
            if null_count > 0:
                print(f"  {col}: {null_count}")

        print(f"\nFirst 5 rows:")
        print(df.head().to_string())

        # Save as parquet if tag is provided
        if hasattr(args, 'tag') and args.tag:
            output_path = f"data/{args.tag}.parquet"
            df.to_parquet(output_path, index=False)
            print(f"\nSaved to parquet: {output_path}")

    except Exception as e:
        print(f"Error loading CSV: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_odds(args):
    """Fetch NFL odds and save to JSON."""
    load_dotenv()
    
    api_key = os.getenv('ODDS_API_KEY')
    if not api_key:
        print("Error: ODDS_API_KEY not found in environment variables", file=sys.stderr)
        print("Make sure to set it in your .env file", file=sys.stderr)
        sys.exit(1)
    
    try:
        print("Fetching NFL odds...")
        odds_data = get_totals_spreads(api_key)
        
        # Create output directory if it doesn't exist
        output_path = Path(args.out)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save raw odds data
        with open(output_path, 'w') as f:
            json.dump(odds_data, f, indent=2)
        
        print(f"Odds data saved to: {output_path}")
        
        # Also create and display implied totals
        df_totals = implied_team_totals(odds_data)
        print(f"\nFound {len(df_totals)} games with odds")
        
        if not df_totals.empty:
            print("\nImplied team totals:")
            print(df_totals[['home_team', 'away_team', 'spread', 'total', 
                           'home_implied_total', 'away_implied_total']].to_string(index=False))
        
    except Exception as e:
        print(f"Error fetching odds: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_import_player_props(args):
    """Import player props CSV and save as parquet."""
    try:
        df, issues = load_player_props(args.csv)

        if issues:
            print("Issues found in player props data:")
            for issue in issues:
                print(f"  - {issue}")

            if any("Missing required columns" in issue for issue in issues):
                print("Cannot proceed with missing required columns.")
                sys.exit(1)

        # Create data directory if it doesn't exist
        output_path = Path("data/player_props.parquet")
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Save as parquet
        df.to_parquet(output_path, index=False)

        print(f"Player props imported successfully: {len(df)} rows")
        print(f"Saved to: {output_path}")

        if issues:
            print(f"Note: {len(issues)} issues found but import proceeded")

    except Exception as e:
        print(f"Error importing player props: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_import_team_props(args):
    """Import team props CSV and save as parquet."""
    try:
        df, issues = load_team_props(args.csv)

        if issues:
            print("Issues found in team props data:")
            for issue in issues:
                print(f"  - {issue}")

            if any("Missing required columns" in issue for issue in issues):
                print("Cannot proceed with missing required columns.")
                sys.exit(1)

        # Create data directory if it doesn't exist
        output_path = Path("data/team_props.parquet")
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Save as parquet
        df.to_parquet(output_path, index=False)

        print(f"Team props imported successfully: {len(df)} rows")
        print(f"Saved to: {output_path}")

        if issues:
            print(f"Note: {len(issues)} issues found but import proceeded")

    except Exception as e:
        print(f"Error importing team props: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_import_roles(args):
    """Import player roles CSV and save as parquet."""
    try:
        df, issues = load_roles_notes(args.csv)

        if issues:
            print("Issues found in roles data:")
            for issue in issues:
                print(f"  - {issue}")

            if any("Missing required columns" in issue for issue in issues):
                print("Cannot proceed with missing required columns.")
                sys.exit(1)

        # Create data directory if it doesn't exist
        output_path = Path("data/roles_notes.parquet")
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Save as parquet
        df.to_parquet(output_path, index=False)

        print(f"Player roles imported successfully: {len(df)} rows")
        print(f"Saved to: {output_path}")

        if issues:
            print(f"Note: {len(issues)} issues found but import proceeded")

    except Exception as e:
        print(f"Error importing roles: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_needs(args):
    """Analyze missing data and generate needs report."""
    try:
        # Load main player data
        if not Path(args.canonical).exists():
            print(f"Canonical file not found: {args.canonical}")
            sys.exit(1)

        if args.canonical.endswith('.parquet'):
            players_df = pd.read_parquet(args.canonical)
        else:
            players_df, _ = load_csv(args.canonical)

        # Load optional data files
        team_props_df = None
        if Path("data/team_props.parquet").exists():
            team_props_df = pd.read_parquet("data/team_props.parquet")

        player_props_df = None
        if Path("data/player_props.parquet").exists():
            player_props_df = pd.read_parquet("data/player_props.parquet")

        roles_df = None
        if Path("data/roles_notes.parquet").exists():
            roles_df = pd.read_parquet("data/roles_notes.parquet")

        games_df = None
        if Path("data/games.parquet").exists():
            games_df = pd.read_parquet("data/games.parquet")

        # Detect missing data
        detector = NeedsDetector()
        missing_items = detector.detect_missing(
            players_df, team_props_df, player_props_df, games_df, roles_df
        )

        # Generate report
        report = detector.generate_needs_report(missing_items, players_df)
        print(report)

        # Generate templates if requested
        if args.generate_templates:
            templates = detector.generate_templates("templates")
            print(f"\nGenerated {len(templates)} template files:")
            for name, path in templates.items():
                print(f"  - {path}")

        # Save report to file if requested
        if args.output:
            with open(args.output, 'w') as f:
                f.write(report)
            print(f"\nReport saved to: {args.output}")

    except Exception as e:
        print(f"Error analyzing needs: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_project(args):
    """Generate projections with confidence scoring."""
    try:
        # Load main player data
        if not Path(args.canonical).exists():
            print(f"Canonical file not found: {args.canonical}")
            sys.exit(1)

        if args.canonical.endswith('.parquet'):
            players_df = pd.read_parquet(args.canonical)
        else:
            players_df, _ = load_csv(args.canonical)

        # Load optional data files
        team_props_df = None
        if args.team_props and Path(args.team_props).exists():
            team_props_df = pd.read_parquet(args.team_props)
        elif Path("data/team_props.parquet").exists():
            team_props_df = pd.read_parquet("data/team_props.parquet")

        player_props_df = None
        if args.player_props and Path(args.player_props).exists():
            player_props_df = pd.read_parquet(args.player_props)
        elif Path("data/player_props.parquet").exists():
            player_props_df = pd.read_parquet("data/player_props.parquet")

        roles_df = None
        if args.roles and Path(args.roles).exists():
            roles_df = pd.read_parquet(args.roles)
        elif Path("data/roles_notes.parquet").exists():
            roles_df = pd.read_parquet("data/roles_notes.parquet")

        # Analyze needs for confidence scoring
        detector = NeedsDetector()
        missing_items = detector.detect_missing(
            players_df, team_props_df, player_props_df, None, roles_df
        )

        # Calculate confidence scores
        confidences = []
        for _, player in players_df.iterrows():
            confidence = detector.confidence_score(player, missing_items)
            confidences.append(confidence)

        players_df['confidence'] = confidences

        # Create basic projections (placeholder - would use actual models)
        projections_df = players_df.copy()
        projections_df['proj_mean'] = np.random.normal(15, 5, len(players_df))  # Placeholder
        projections_df['p50'] = projections_df['proj_mean']
        projections_df['p75'] = projections_df['proj_mean'] * 1.2
        projections_df['p90'] = projections_df['proj_mean'] * 1.4
        projections_df['own_hat'] = np.random.uniform(0.05, 0.25, len(players_df))  # Placeholder
        projections_df['leverage'] = np.random.uniform(0.8, 1.2, len(players_df))  # Placeholder

        # Add missing fields info
        missing_by_player = {}
        for item in missing_items:
            if item.player_name:
                if item.player_name not in missing_by_player:
                    missing_by_player[item.player_name] = []
                missing_by_player[item.player_name].append(item.description)

        projections_df['missing_fields'] = projections_df['player_name'].apply(
            lambda x: '; '.join(missing_by_player.get(x, []))
        )

        # Select output columns
        output_cols = ['player_name', 'team', 'position', 'proj_mean', 'p50', 'p75', 'p90',
                      'own_hat', 'leverage', 'confidence', 'missing_fields']

        # Add salary if available
        if 'salary' in projections_df.columns:
            output_cols.insert(3, 'salary')

        # Add team_total if available
        if 'team_total' in projections_df.columns:
            output_cols.insert(-4, 'team_total')

        final_df = projections_df[output_cols]

        # Save projections
        output_path = args.output or "data/week1_projections.csv"
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        final_df.to_csv(output_path, index=False)

        print(f"Projections generated: {len(final_df)} players")
        print(f"Average confidence: {final_df['confidence'].mean():.1%}")
        print(f"Saved to: {output_path}")

        # Show top 10 lowest confidence players
        lowest_conf = final_df.nsmallest(10, 'confidence')
        print(f"\nTop 10 lowest confidence players:")
        for _, player in lowest_conf.iterrows():
            print(f"  {player['player_name']} ({player['position']}): {player['confidence']:.1%}")

    except Exception as e:
        print(f"Error generating projections: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_weather(args):
    """Fetch weather data for a specific location and date."""
    from .fetch_odds import fetch_weather_data

    try:
        weather_data = fetch_weather_data(args.lat, args.lon, args.date)

        if args.output:
            import json
            with open(args.output, 'w') as f:
                json.dump(weather_data, f, indent=2)
            print(f"Weather data saved to: {args.output}")
        else:
            import json
            print(json.dumps(weather_data, indent=2))

    except Exception as e:
        print(f"Error fetching weather data: {e}")
        sys.exit(1)


def cmd_sleeper_players(args):
    """Fetch Sleeper players data."""
    try:
        print("Fetching Sleeper players data...")
        df = get_players()

        print(f"Retrieved {len(df)} players from Sleeper API")

        # Create output directory if it doesn't exist
        output_path = Path(args.out)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Save to parquet
        df.to_parquet(output_path, index=False)
        print(f"Sleeper players data saved to: {output_path}")

        # Show summary
        print(f"\nSummary:")
        print(f"Total players: {len(df)}")
        print(f"Active players: {df['active'].sum()}")
        print(f"Teams represented: {df[df['team'] != '']['team'].nunique()}")
        print(f"Positions: {', '.join(sorted(df['position'].unique()))}")

    except Exception as e:
        print(f"Error fetching Sleeper players: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_sleeper_state(args):
    """Fetch Sleeper NFL state."""
    try:
        print("Fetching Sleeper NFL state...")
        state = get_state()

        print("Current NFL State:")
        for key, value in state.items():
            print(f"  {key}: {value}")

    except Exception as e:
        print(f"Error fetching Sleeper state: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_sleeper_trending(args):
    """Fetch Sleeper trending players."""
    try:
        print(f"Fetching Sleeper trending players ({args.kind}, {args.hours}h, limit {args.limit})...")
        df = get_trending(kind=args.kind, lookback_hours=args.hours, limit=args.limit)

        if df.empty:
            print("No trending players found.")
            return

        print(f"Retrieved {len(df)} trending players")

        # Create output directory if it doesn't exist
        output_path = Path(args.out)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Save to parquet
        df.to_parquet(output_path, index=False)
        print(f"Sleeper trending data saved to: {output_path}")

        # Show top 10
        print(f"\nTop 10 trending {args.kind} players:")
        print(df.head(10)[['player_id', 'count']].to_string(index=False))

    except Exception as e:
        print(f"Error fetching Sleeper trending data: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_depth(args):
    """Fetch roster data for NFL teams (NOT actual depth charts)."""
    try:
        # Default to all NFL teams if none specified
        if args.teams:
            teams = args.teams
        else:
            # All 32 NFL teams
            teams = [
                'ARI', 'ATL', 'BAL', 'BUF', 'CAR', 'CHI', 'CIN', 'CLE',
                'DAL', 'DEN', 'DET', 'GB', 'HOU', 'IND', 'JAC', 'KC',
                'LV', 'LAC', 'LAR', 'MIA', 'MIN', 'NE', 'NO', 'NYG',
                'NYJ', 'PHI', 'PIT', 'SF', 'SEA', 'TB', 'TEN', 'WAS'
            ]

        print(f"Fetching roster data for {len(teams)} teams...")
        print("NOTE: This provides roster composition only, not depth charts.")
        print("Use roles_notes.csv for authoritative depth chart information.")

        df = depth_charts_for_teams(teams)

        if df.empty:
            print("No roster data found.")
            return

        print(f"Retrieved roster data for {len(df)} players")

        # Create output directory if it doesn't exist
        output_path = Path(args.out)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Save to parquet
        df.to_parquet(output_path, index=False)
        print(f"Roster data saved to: {output_path}")

        # Show summary by team and position
        print(f"\nRoster Summary:")
        for team in sorted(df['team'].unique())[:5]:
            team_df = df[df['team'] == team]
            pos_counts = team_df['position'].value_counts()
            pos_summary = ', '.join([f"{pos}={count}" for pos, count in pos_counts.items()])
            print(f"{team}: {pos_summary}")

        if len(df['team'].unique()) > 5:
            print(f"... and {len(df['team'].unique()) - 5} more teams")

    except Exception as e:
        print(f"Error fetching roster data: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_backtest_2024w1(args):
    """Run 2024 Week 1 backtest analysis."""
    try:
        print(f"Loading 2024 Week 1 data from: {args.csv}")
        df = load_fc_week1_2024(args.csv)

        if df.empty:
            print("No data loaded for backtest.")
            return

        print(f"Loaded {len(df)} player records")

        # Compute residuals
        print("Computing residuals...")
        residuals_df = compute_residuals(df)

        # Summarize residuals
        print("Summarizing residuals by position and archetype...")
        summary = summarize_residuals(residuals_df)

        # Build adjusters
        print("Building gentle adjustment parameters...")
        adjusters = build_residual_adjusters(
            summary,
            lambda_shrink=getattr(args, 'lambda_shrink', 0.35),
            mu_cap=getattr(args, 'mu_cap', 0.6),
            sigma_scale=getattr(args, 'sigma_scale', 0.03),
            sigma_cap=getattr(args, 'sigma_cap', 1.10)
        )

        # Write adjusters JSON if requested
        if args.emit_adjusters:
            adjusters_path = Path(args.emit_adjusters)
            adjusters_path.parent.mkdir(parents=True, exist_ok=True)

            with open(adjusters_path, 'w') as f:
                json.dump(adjusters, f, indent=2)
            print(f"Adjusters saved to: {adjusters_path}")

        # Write backtest report
        out_csv_path = Path(args.out)
        out_txt_path = Path(args.report)

        out_csv_path.parent.mkdir(parents=True, exist_ok=True)
        out_txt_path.parent.mkdir(parents=True, exist_ok=True)

        write_backtest_report(residuals_df, summary, str(out_csv_path), str(out_txt_path))

        print(f"Backtest results saved to: {out_csv_path}")
        print(f"Backtest report saved to: {out_txt_path}")

        # Print key statistics
        global_stats = summary.get('global', {})
        print(f"\n=== 2024 Week 1 Backtest Summary ===")
        print(f"Players analyzed: {global_stats.get('count', 0)}")
        print(f"Mean Error (Bias): {global_stats.get('mean_error', 0):.3f}")
        print(f"MAE: {global_stats.get('mae', 0):.3f}")
        print(f"RMSE: {global_stats.get('rmse', 0):.3f}")
        print(f"Correlation: {global_stats.get('pearson_r', 0):.3f}")

        # Show position-level stats
        print(f"\n=== By Position ===")
        for pos, stats in summary.get('by_pos', {}).items():
            print(f"{pos}: Bias={stats.get('mean_error', 0):.2f}, "
                  f"MAE={stats.get('mae', 0):.2f}, "
                  f"Count={stats.get('count', 0)}")

    except Exception as e:
        print(f"Error running backtest: {e}", file=sys.stderr)
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='NFL Projections CLI')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Load command
    load_parser = subparsers.add_parser('load', help='Load and analyze CSV file')
    load_parser.add_argument('--csv', required=True, help='Path to CSV file')
    load_parser.add_argument('--tag', help='Tag for output parquet file (e.g., 2024w1)')
    load_parser.set_defaults(func=cmd_load)

    # Odds command
    odds_parser = subparsers.add_parser('odds', help='Fetch NFL odds')
    odds_parser.add_argument('--out', default='data/odds_week1.json',
                           help='Output path for odds JSON file')
    odds_parser.set_defaults(func=cmd_odds)

    # Import player props command
    import_player_parser = subparsers.add_parser('import-player-props',
                                                help='Import player props CSV')
    import_player_parser.add_argument('--csv', required=True, help='Path to player props CSV')
    import_player_parser.set_defaults(func=cmd_import_player_props)

    # Import team props command
    import_team_parser = subparsers.add_parser('import-team-props',
                                              help='Import team props CSV')
    import_team_parser.add_argument('--csv', required=True, help='Path to team props CSV')
    import_team_parser.set_defaults(func=cmd_import_team_props)

    # Import roles command
    import_roles_parser = subparsers.add_parser('import-roles',
                                               help='Import player roles CSV')
    import_roles_parser.add_argument('--csv', required=True, help='Path to roles CSV')
    import_roles_parser.set_defaults(func=cmd_import_roles)

    # Needs analysis command
    needs_parser = subparsers.add_parser('needs', help='Analyze missing data needs')
    needs_parser.add_argument('--canonical', required=True,
                             help='Path to canonical player data file')
    needs_parser.add_argument('--output', help='Save report to file')
    needs_parser.add_argument('--generate-templates', action='store_true',
                             help='Generate CSV templates for missing data')
    needs_parser.set_defaults(func=cmd_needs)

    # Project command
    project_parser = subparsers.add_parser('project', help='Generate projections')
    project_parser.add_argument('--canonical', required=True,
                               help='Path to canonical player data file')
    project_parser.add_argument('--player-props', help='Path to player props parquet file')
    project_parser.add_argument('--team-props', help='Path to team props parquet file')
    project_parser.add_argument('--roles', help='Path to roles parquet file')
    project_parser.add_argument('--depth', help='Path to depth charts parquet file')
    project_parser.add_argument('--output', help='Output path for projections CSV')
    project_parser.add_argument('--use-2024w1-residuals', action='store_true',
                               help='Apply gentle 2024 Week 1 residual nudges')
    project_parser.add_argument('--adjusters',
                               help='Path to adjusters JSON file (required with --use-2024w1-residuals)')
    project_parser.set_defaults(func=cmd_project)

    # Sleeper players command
    sleeper_players_parser = subparsers.add_parser('sleeper-players',
                                                  help='Fetch Sleeper players data')
    sleeper_players_parser.add_argument('--out', default='data/sleeper_players.parquet',
                                       help='Output path for players parquet file')
    sleeper_players_parser.set_defaults(func=cmd_sleeper_players)

    # Sleeper state command
    sleeper_state_parser = subparsers.add_parser('sleeper-state',
                                                help='Fetch Sleeper NFL state')
    sleeper_state_parser.set_defaults(func=cmd_sleeper_state)

    # Sleeper trending command
    sleeper_trending_parser = subparsers.add_parser('sleeper-trending',
                                                   help='Fetch Sleeper trending players')
    sleeper_trending_parser.add_argument('--kind', default='add', choices=['add', 'drop'],
                                        help='Type of trending data (add or drop)')
    sleeper_trending_parser.add_argument('--hours', type=int, default=24,
                                        help='Hours to look back for trending data')
    sleeper_trending_parser.add_argument('--limit', type=int, default=50,
                                        help='Maximum number of players to return')
    sleeper_trending_parser.add_argument('--out', default='data/sleeper_trending.parquet',
                                        help='Output path for trending parquet file')
    sleeper_trending_parser.set_defaults(func=cmd_sleeper_trending)

    # Roster data command (NOT depth charts)
    depth_parser = subparsers.add_parser('depth', help='Fetch NFL team roster data (NOT depth charts)')
    depth_parser.add_argument('--teams', nargs='*',
                             help='Team abbreviations (e.g., BUF KC). If not specified, fetches all teams')
    depth_parser.add_argument('--out', default='data/roster_data.parquet',
                             help='Output path for roster data parquet file')
    depth_parser.set_defaults(func=cmd_depth)

    # Backtest 2024 Week 1 command
    backtest_parser = subparsers.add_parser('backtest-2024w1',
                                           help='Run 2024 Week 1 backtest analysis')
    backtest_parser.add_argument('--csv', required=True,
                                help='Path to 2024 Week 1 CSV file with projections and scores')
    backtest_parser.add_argument('--out', default='data/backtest_2024w1.csv',
                                help='Output path for detailed backtest CSV')
    backtest_parser.add_argument('--report', default='data/backtest_2024w1.txt',
                                help='Output path for backtest report')
    backtest_parser.add_argument('--emit-adjusters',
                                help='Output path for adjustment parameters JSON')
    backtest_parser.add_argument('--lambda-shrink', type=float, default=0.35,
                                help='Shrinkage factor for mean error (default: 0.35)')
    backtest_parser.add_argument('--mu-cap', type=float, default=0.6,
                                help='Maximum absolute nudge in points (default: 0.6)')
    backtest_parser.add_argument('--sigma-scale', type=float, default=0.03,
                                help='Sigma multiplier per point of absolute bias (default: 0.03)')
    backtest_parser.add_argument('--sigma-cap', type=float, default=1.10,
                                help='Maximum sigma multiplier (default: 1.10)')
    backtest_parser.set_defaults(func=cmd_backtest_2024w1)

    # Weather command
    weather_parser = subparsers.add_parser('weather', help='Fetch weather data from Meteomatics API')
    weather_parser.add_argument('--lat', type=float, required=True, help='Latitude')
    weather_parser.add_argument('--lon', type=float, required=True, help='Longitude')
    weather_parser.add_argument('--date', required=True, help='Date in YYYY-MM-DD format')
    weather_parser.add_argument('--output', help='Output JSON file path (optional)')
    weather_parser.set_defaults(func=cmd_weather)

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    args.func(args)


if __name__ == '__main__':
    main()
